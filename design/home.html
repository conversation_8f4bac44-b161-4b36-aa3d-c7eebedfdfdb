<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect" />
    <link
      as="style"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Manrope%3Awght%40400%3B500%3B700%3B800&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900"
      onload="this.rel='stylesheet'"
      rel="stylesheet"
    />
    <title>Stitch Design</title>
    <link href="data:image/x-icon;base64," rel="icon" type="image/x-icon" />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <link
      href="https://fonts.googleapis.com/icon?family=Material+Icons"
      rel="stylesheet"
    />
  </head>
  <body style="font-family: Manrope, 'Noto Sans', sans-serif">
    <div
      class="relative flex size-full min-h-screen flex-col bg-slate-50 group/design-root overflow-x-hidden"
    >
      <div class="layout-container flex h-full grow flex-col">
        <header
          class="flex items-center justify-between whitespace-nowrap border-b border-solid border-slate-200 bg-white px-6 py-3 shadow-sm"
        >
          <div class="flex items-center gap-3 text-slate-900">
            <svg
              class="h-8 w-8 text-blue-600"
              fill="none"
              viewBox="0 0 48 48"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M24 45.8096C19.6865 45.8096 15.4698 44.5305 11.8832 42.134C8.29667 39.7376 5.50128 36.3314 3.85056 32.3462C2.19985 28.361 1.76794 23.9758 2.60947 19.7452C3.451 15.5145 5.52816 11.6284 8.57829 8.5783C11.6284 5.52817 15.5145 3.45101 19.7452 2.60948C23.9758 1.76795 28.361 2.19986 32.3462 3.85057C36.3314 5.50129 39.7376 8.29668 42.134 11.8833C44.5305 15.4698 45.8096 19.6865 45.8096 24L24 24L24 45.8096Z"
                fill="currentColor"
              ></path>
            </svg>
            <h2
              class="text-slate-900 text-xl font-bold leading-tight tracking-tighter"
            >
              StudySmart
            </h2>
          </div>
          <div class="flex flex-1 justify-end gap-4">
            <button
              class="flex h-10 cursor-pointer items-center justify-center rounded-full bg-slate-100 px-3 text-slate-700 hover:bg-slate-200"
            >
              <span class="material-icons text-xl">help_outline</span>
            </button>
            <div
              class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10 border border-slate-200"
              style="
                background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuABXXn2SyZAWq7L9xC5nzTnKuoqI_pUWsBqX4CnarxW0sn6Z_l61grTvw1DuHkYEt0RybNgnPRUZ00OdRxq7vMcmVnQVh38eZPpD_eWzlN5yIiQDqXHIx_zhCpWgY97KscBtyFRsIsWqHYSmJH4RRJ2uXfi5twOGotnnZ3pC9bQEhredEONNzeRsLmRWa-LI9KKrS_XBMB8DBe-sBp59WBh7C_qCXgn2NIwgmyxHwk1Qh2MSJWXHxE8_72omFJRTZanOv9vu4rj49Mi');
              "
            ></div>
          </div>
        </header>
        <div class="flex flex-1">
          <aside
            class="flex w-64 flex-col border-r border-slate-200 bg-white p-4 shadow-sm"
          >
            <nav class="flex flex-col gap-2">
              <a
                class="flex items-center gap-3 rounded-lg bg-blue-50 px-3 py-2.5 text-blue-600 transition-colors hover:bg-blue-100"
                href="#"
              >
                <span class="material-icons text-xl">home</span>
                <span class="text-sm font-medium">Home</span>
              </a>
              <a
                class="flex items-center gap-3 rounded-lg px-3 py-2.5 text-slate-700 transition-colors hover:bg-slate-100"
                href="#"
              >
                <span class="material-icons text-xl">description</span>
                <span class="text-sm font-medium">Documents</span>
              </a>
              <a
                class="flex items-center gap-3 rounded-lg px-3 py-2.5 text-slate-700 transition-colors hover:bg-slate-100"
                href="#"
              >
                <span class="material-icons text-xl">chat_bubble_outline</span>
                <span class="text-sm font-medium">Chat</span>
              </a>
              <a
                class="flex items-center gap-3 rounded-lg px-3 py-2.5 text-slate-700 transition-colors hover:bg-slate-100"
                href="#"
              >
                <span class="material-icons text-xl">quiz</span>
                <span class="text-sm font-medium">Quizzes</span>
              </a>
              <a
                class="flex items-center gap-3 rounded-lg px-3 py-2.5 text-slate-700 transition-colors hover:bg-slate-100"
                href="#"
              >
                <span class="material-icons text-xl">style</span>
                <span class="text-sm font-medium">Flashcards</span>
              </a>
            </nav>
          </aside>
          <main class="flex-1 p-8">
            <div class="mb-8">
              <p
                class="text-slate-900 text-3xl font-bold leading-tight tracking-tight"
              >
                Welcome back, Sarah
              </p>
            </div>
            <div
              class="mb-8 rounded-xl bg-gradient-to-br from-blue-500 to-blue-700 p-8 text-white shadow-lg @container"
            >
              <div
                class="flex flex-col items-start justify-between gap-6 @lg:flex-row"
              >
                <div class="flex-1">
                  <p
                    class="mb-1 text-sm font-medium uppercase tracking-wider text-blue-200"
                  >
                    Get Started
                  </p>
                  <h2 class="mb-3 text-2xl font-bold leading-tight">
                    Start Your Learning Journey
                  </h2>
                  <p class="mb-6 text-blue-100">
                    Upload your documents and unlock the power of AI-driven
                    learning.
                  </p>
                  <button
                    class="flex items-center justify-center gap-2 rounded-lg bg-white px-5 py-3 text-sm font-semibold text-blue-600 shadow-md transition-transform hover:scale-105"
                  >
                    <span class="material-icons text-lg">upload_file</span>
                    <span>Upload Document</span>
                  </button>
                </div>
                <div class="w-full @lg:w-1/3">
                  <div
                    class="aspect-video w-full rounded-lg bg-cover bg-center shadow-md"
                    style="
                      background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuBWlPYqphzkGwAbyCv9MSoPpV7TqztZVOrBoObgN06YWkz84dGkj544Mk_G7nI96zQwFecd61jX0IpuMmXwDexlhBfg2eFyojx05havod14TAI7HblrfmIT04nEFaWd8_NWMxYaMoJ5zyQt8k29UnEsqNiV3WDXUDy4aBuwgXGXthWeOCyQoCPbOw0CnoQon8tOD_tPbjlNTNTpFECaUF6hk-n0sQ2XAlJKzvf81qFPBz0AwSCxRReiRtMDeRoWyBdjVHUlmVjz53G-');
                    "
                  ></div>
                </div>
              </div>
            </div>
            <h2
              class="mb-6 text-2xl font-bold leading-tight tracking-tight text-slate-900"
            >
              Recent Documents
            </h2>
            <div
              class="grid grid-cols-[repeat(auto-fit,minmax(200px,1fr))] gap-6"
            >
              <div
                class="group flex cursor-pointer flex-col gap-3 rounded-xl bg-white p-4 shadow-sm transition-all hover:shadow-lg"
              >
                <div
                  class="aspect-video w-full rounded-lg bg-cover bg-center"
                  style="
                    background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuAl8Z7_qJdSdwF1-swjwHXv2zniO-228PDhJYJ6OJIsyf0RqqUr53FYgkzVVwYhgyGW6n3KtuSPDLxADiZBbqdYBz6-UWDp2pkZ2rFf6xYdc8kQjgKUdIbe3uCBvKBsXOwjhXRSDl_kiGQT-Fr8OUmROvS9xJXZVLNlDVQ3mjmz80FrV9aXt3nI7B29Ou13cl1UBmbuhOhD79HOsjE7eGKhlrOBJli3Y91snU865x1rKWqo7mpn9TEPeVjnrEFXZVffWk5j-TPVHIKm');
                  "
                ></div>
                <p
                  class="text-base font-semibold text-slate-800 group-hover:text-blue-600"
                >
                  History Notes
                </p>
              </div>
              <div
                class="group flex cursor-pointer flex-col gap-3 rounded-xl bg-white p-4 shadow-sm transition-all hover:shadow-lg"
              >
                <div
                  class="aspect-video w-full rounded-lg bg-cover bg-center"
                  style="
                    background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuAWkawGJyquSqbYZtXUHfpbkdYx8MKLnMJxhVHdyn32YGaTkL8njTL_9bwCd6CYx7LYMQXg0XvEAY9MjzCg3I6CLgfhZnXJyAnuWxKmBmL8TYWhnVvNYG-O8es41ZbhK5kRRsCXCe1cTCWiRu7dBUmUVN9lR_wS2fG_Fk4T1ZC-tl8UGtMq-SmWYZCEZ9F5yJvhlCU_sar99Bv-5SE81FKcBV9lOrJEqu27TWMtIKKSh08kBC5tJPwB506R2bJXSfw_9crqJIzphpv_');
                  "
                ></div>
                <p
                  class="text-base font-semibold text-slate-800 group-hover:text-blue-600"
                >
                  Math Formulas
                </p>
              </div>
              <div
                class="group flex cursor-pointer flex-col gap-3 rounded-xl bg-white p-4 shadow-sm transition-all hover:shadow-lg"
              >
                <div
                  class="aspect-video w-full rounded-lg bg-cover bg-center"
                  style="
                    background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuDvr6t5lBhdpVgun3zo1lQ88FYKakY-8Y0s_X21nNYXyZ3_NJ01K-CPzthwJYqGZGOmfj0AYVgEo55IsLFbCu3AZ-KeAox0dHMRnPDTwjOFWSUYbPib4zmhB7zh_qY_tWA2JCt0HdjuL-vJhWaLkEsSMkefU_OloRGuZPBnNZKfp0Ntz1D81HdEPPEwg7ZqnySvPl1f3G5-c1h8jD8MgovK3GKTKEqx8xQE8A-b2K9TYlTqpZHlaapenxzvIeShrorm8uB_Y1da5C5_');
                  "
                ></div>
                <p
                  class="text-base font-semibold text-slate-800 group-hover:text-blue-600"
                >
                  Science Concepts
                </p>
              </div>
              <div
                class="group flex cursor-pointer flex-col gap-3 rounded-xl bg-white p-4 shadow-sm transition-all hover:shadow-lg"
              >
                <div
                  class="aspect-video w-full rounded-lg bg-cover bg-center"
                  style="
                    background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuDtW2j3TLjnekhx4NUXCTI9TdqHzci7AXmcNgvxiujXB-tS3Mp5CBHGPz8g6TvzfxFuF8Bp7T7slbT5YCIozKZzDutkNnqgfZXnse-3-5-ndRnX0_9V13U44WVGUqkj4ElNAbxIr6E2zW0ALaAPAWGEhbOR5HgPd0ivsTUnh97bfTXgrlGTeE9-M6ykDg6IWbmd8oJuoH4FDkBhoM-JtUPtIM-2oSrJwu0LrcQuuYqg6DC5cnmrrqtVH9VmpyOQwcjWYsmrBVGd_loS');
                  "
                ></div>
                <p
                  class="text-base font-semibold text-slate-800 group-hover:text-blue-600"
                >
                  Literature Analysis
                </p>
              </div>
              <div
                class="group flex cursor-pointer flex-col gap-3 rounded-xl bg-white p-4 shadow-sm transition-all hover:shadow-lg"
              >
                <div
                  class="aspect-video w-full rounded-lg bg-cover bg-center"
                  style="
                    background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuCoNHlVbAuO7SoTPvxA7HqpgbSwMmPw4A_0XRdo0j-YfZwsvcwhsy5rGX9dO2tSA8aWjObBg48fVW8-FOVQpGku_RnvYyA3e3UQ76PdhvEV1xI1U9ZOQ3x7gQsMimC-UVwfoH_L0nWQPbTva1P11j1PRvXHF5DPmtBpzP-PyveSV7t1xm_b_hZ18GyMMe86h5MkXtPUR9VPbXWzJP7fZkwV3xtmwIeadac_CBsDCRlIKbroP0oOklE3nNgOtS4fGmF8c39rZZTsO2Nc');
                  "
                ></div>
                <p
                  class="text-base font-semibold text-slate-800 group-hover:text-blue-600"
                >
                  Economics Theories
                </p>
              </div>
              <div
                class="group flex cursor-pointer flex-col gap-3 rounded-xl bg-white p-4 shadow-sm transition-all hover:shadow-lg"
              >
                <div
                  class="aspect-video w-full rounded-lg bg-cover bg-center"
                  style="
                    background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuAJzxYDsRfLQFuH4HW2vNL-ewLdUUKCHEd_mkXeJD_9BeXVm7LHZtAcyPaf2wjmD7-vyH3YiF3KkCosSRZC2-EWXo7wDfCpM7Si2PFbe9agtlEafAEwg7p6TyT5ccTah4aV8-2M5B5YVehtfZzeEjDpavEjMW3c6y7gvVOD-Jo-9SmhJefDjwZM5OfVlmJ91nrtso-BxRGWpoAeV4evza613wocD4ZNP7L0WXa7idZdVAudkxJDEJDKU6hpP7eDm_cWj1g7zO9yGLFA');
                  "
                ></div>
                <p
                  class="text-base font-semibold text-slate-800 group-hover:text-blue-600"
                >
                  Psychology Studies
                </p>
              </div>
            </div>
          </main>
        </div>
      </div>
      <button
        class="fixed bottom-8 right-8 z-50 flex h-16 w-16 items-center justify-center rounded-full bg-blue-600 text-white shadow-xl transition-transform hover:scale-110 hover:bg-blue-700"
        title="AI Chat"
      >
        <span class="material-icons text-3xl">smart_toy</span>
      </button>
    </div>
  </body>
</html>
