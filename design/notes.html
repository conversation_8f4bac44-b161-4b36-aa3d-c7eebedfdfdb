<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect" />
    <link
      as="style"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Lexend%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Geist+Mono&amp;family=Inter:wght@400;500;600;700"
      onload="this.rel='stylesheet'"
      rel="stylesheet"
    />
    <title>StudyAI - Markdown Editor</title>
    <link href="data:image/x-icon;base64," rel="icon" type="image/x-icon" />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <link
      href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined"
      rel="stylesheet"
    />
    <style>
      .geist-mono {
        font-family: "Geist Mono", monospace;
      }
      .font-inter {
        font-family: "Inter", sans-serif;
      }
      .code-block {
        background-color: #1e293b;
        color: #e2e8f0;
        padding: 1rem;
        border-radius: 0.5rem;
        overflow-x: auto;
      }
      .code-block .token.keyword {
        color: #fb7185;
      }
      .code-block .token.function {
        color: #818cf8;
      }
      .code-block .token.string {
        color: #a3e635;
      }
      .code-block .token.comment {
        color: #9ca3af;
      }
      .code-block .token.operator {
        color: #facc15;
      }
      .code-block .token.punctuation {
        color: #cbd5e1;
      }
      .code-block .token.number {
        color: #38bdf8;
      }
      ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      ::-webkit-scrollbar-track {
        background: #1e293b;
      }
      ::-webkit-scrollbar-thumb {
        background: #475569;
        border-radius: 4px;
      }
      ::-webkit-scrollbar-thumb:hover {
        background: #64748b;
      }
      .split-view-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        height: calc(100vh - 200px);
      }
      @media (max-width: 768px) {
        .split-view-container {
          grid-template-columns: 1fr;
        }
        .md-preview {
          display: none;
        }
        .md-preview.active {
          display: block;
        }
        .md-editor.active {
          display: block;
        }
        .md-editor:not(.active) {
          display: none;
        }
      }
    </style>
  </head>
  <body
    class="bg-[#111518] dark text-white font-inter"
    style="font-family: Lexend, 'Noto Sans', 'Inter', sans-serif"
  >
    <div
      class="relative flex size-full min-h-screen flex-col group/design-root overflow-x-hidden"
    >
      <div class="layout-container flex h-full grow flex-col">
        <header
          class="flex items-center justify-between whitespace-nowrap border-b border-solid border-b-[#283139] px-6 py-4 md:px-10"
        >
          <div class="flex items-center gap-3">
            <div class="size-7 text-[#1383eb]">
              <svg
                fill="none"
                viewBox="0 0 48 48"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g clip-path="url(#clip0_6_543)">
                  <path
                    d="M42.1739 20.1739L27.8261 5.82609C29.1366 7.13663 28.3989 10.1876 26.2002 13.7654C24.8538 15.9564 22.9595 18.3449 20.6522 20.6522C18.3449 22.9595 15.9564 24.8538 13.7654 26.2002C10.1876 28.3989 7.13663 29.1366 5.82609 27.8261L20.1739 42.1739C21.4845 43.4845 24.5355 42.7467 28.1133 40.548C30.3042 39.2016 32.6927 37.3073 35 35C37.3073 32.6927 39.2016 30.3042 40.548 28.1133C42.7467 24.5355 43.4845 21.4845 42.1739 20.1739Z"
                    fill="currentColor"
                  ></path>
                  <path
                    clip-rule="evenodd"
                    d="M7.24189 26.4066C7.31369 26.4411 7.64204 26.5637 8.52504 26.3738C9.59462 26.1438 11.0343 25.5311 12.7183 24.4963C14.7583 23.2426 17.0256 21.4503 19.238 19.238C21.4503 17.0256 23.2426 14.7583 24.4963 12.7183C25.5311 11.0343 26.1438 9.59463 26.3738 8.52504C26.5637 7.64204 26.4411 7.31369 26.4066 7.24189C26.345 7.21246 26.143 7.14535 25.6664 7.1918C24.9745 7.25925 23.9954 7.5498 22.7699 8.14278C20.3369 9.32007 17.3369 11.4915 14.4142 14.4142C11.4915 17.3369 9.32007 20.3369 8.14278 22.7699C7.5498 23.9954 7.25925 24.9745 7.1918 25.6664C7.14534 26.143 7.21246 26.345 7.24189 26.4066ZM29.9001 10.7285C29.4519 12.0322 28.7617 13.4172 27.9042 14.8126C26.465 17.1544 24.4686 19.6641 22.0664 22.0664C19.6641 24.4686 17.1544 26.465 14.8126 27.9042C13.4172 28.7617 12.0322 29.4519 10.7285 29.9001L21.5754 40.747C21.6001 40.7606 21.8995 40.931 22.8729 40.7217C23.9424 40.4916 25.3821 39.879 27.0661 38.8441C29.1062 37.5904 31.3734 35.7982 33.5858 33.5858C35.7982 31.3734 37.5904 29.1062 38.8441 27.0661C39.879 25.3821 40.4916 23.9425 40.7216 22.8729C40.931 21.8995 40.7606 21.6001 40.747 21.5754L29.9001 10.7285ZM29.2403 4.41187L43.5881 18.7597C44.9757 20.1473 44.9743 22.1235 44.6322 23.7139C44.2714 25.3919 43.4158 27.2666 42.252 29.1604C40.8128 31.5022 38.8165 34.012 36.4142 36.4142C34.012 38.8165 31.5022 40.8128 29.1604 42.252C27.2666 43.4158 25.3919 44.2714 23.7139 44.6322C22.1235 44.9743 20.1473 44.9757 18.7597 43.5881L4.41187 29.2403C3.29027 28.1187 3.08209 26.5973 3.21067 25.2783C3.34099 23.9415 3.8369 22.4852 4.54214 21.0277C5.96129 18.0948 8.43335 14.7382 11.5858 11.5858C14.7382 8.43335 18.0948 5.9613 21.0277 4.54214C22.4852 3.8369 23.9415 3.34099 25.2783 3.21067C26.5973 3.08209 28.1187 3.29028 29.2403 4.41187Z"
                    fill="currentColor"
                    fill-rule="evenodd"
                  ></path>
                </g>
                <defs>
                  <clipPath id="clip0_6_543">
                    <rect fill="white" height="48" width="48"></rect>
                  </clipPath>
                </defs>
              </svg>
            </div>
            <h1 class="text-xl font-bold leading-tight tracking-tight">
              StudyAI
            </h1>
          </div>
          <div class="flex items-center gap-4 md:gap-6">
            <nav class="hidden md:flex items-center gap-6">
              <a
                class="text-sm font-medium text-slate-300 hover:text-white transition-colors"
                href="#"
                >My Library</a
              >
              <a
                class="text-sm font-medium text-slate-300 hover:text-white transition-colors"
                href="#"
                >Explore</a
              >
            </nav>
            <button
              class="p-2 rounded-full hover:bg-[#283139] transition-colors text-slate-300 hover:text-white"
            >
              <span class="material-icons-outlined text-2xl"
                >notifications</span
              >
            </button>
            <div
              class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-9 border-2 border-[#283139]"
              style="
                background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuCjc7ltqOtL16xG3SLmW9FLhA6kYyevQhvdSl-pgPE2ARnkIv7sc6zNtzZKUprn3sCfgYbTnPDg6f6zbc0_MBdPsMzqBbauC6RNrvTVvAx71U2_wECN_AiQXziR71-RHBAEM3gkF7VwzqXThOi7re3o1Am989ZlFw7TuVhs6raCxKn9bwdDGAT7Or00OlubLoWkH8JnH13CXaRI0Bsjg22fWuWaQ5Xr_mw9q9-H878YeaLBmO2aStdjsnul8ZgqyYnQ01pCBkbo8F7c');
              "
            ></div>
          </div>
        </header>
        <main class="flex-1 flex flex-col md:flex-row gap-4 p-4 md:p-6">
          <div class="flex-1 flex flex-col bg-[#1c2127] rounded-lg shadow-xl">
            <div
              class="flex items-center justify-between p-3 border-b border-[#283139]"
            >
              <div class="flex items-center gap-2 text-sm">
                <button
                  class="px-3 py-1.5 rounded-md bg-[#1383eb] text-white font-medium"
                  id="view-toggle-edit"
                >
                  Edit
                </button>
                <button
                  class="px-3 py-1.5 rounded-md hover:bg-[#283139] text-slate-300 font-medium"
                  id="view-toggle-preview"
                >
                  Preview
                </button>
              </div>
              <div class="flex items-center gap-2">
                <button
                  class="p-2 rounded hover:bg-[#283139] text-slate-400"
                  title="Bold (Ctrl+B)"
                >
                  <span class="material-icons-outlined text-xl"
                    >format_bold</span
                  >
                </button>
                <button
                  class="p-2 rounded hover:bg-[#283139] text-slate-400"
                  title="Italic (Ctrl+I)"
                >
                  <span class="material-icons-outlined text-xl"
                    >format_italic</span
                  >
                </button>
                <button
                  class="p-2 rounded hover:bg-[#283139] text-slate-400"
                  title="Heading (Ctrl+H)"
                >
                  <span class="material-icons-outlined text-xl">title</span>
                </button>
                <button
                  class="p-2 rounded hover:bg-[#283139] text-slate-400"
                  title="Unordered List (Ctrl+U)"
                >
                  <span class="material-icons-outlined text-xl"
                    >format_list_bulleted</span
                  >
                </button>
                <button
                  class="p-2 rounded hover:bg-[#283139] text-slate-400"
                  title="Ordered List (Ctrl+Shift+L)"
                >
                  <span class="material-icons-outlined text-xl"
                    >format_list_numbered</span
                  >
                </button>
                <button
                  class="p-2 rounded hover:bg-[#283139] text-slate-400"
                  title="Code Block (Ctrl+Shift+C)"
                >
                  <span class="material-icons-outlined text-xl">code</span>
                </button>
                <button
                  class="p-2 rounded hover:bg-[#283139] text-slate-400"
                  title="Link (Ctrl+K)"
                >
                  <span class="material-icons-outlined text-xl">link</span>
                </button>
              </div>
              <span class="text-xs text-slate-500">Saved</span>
            </div>
            <div class="flex-1 split-view-container">
              <div class="md-editor active flex flex-col" id="md-editor-pane">
                <textarea
                  class="flex-1 p-4 bg-transparent text-slate-200 resize-none focus:outline-none geist-mono text-sm leading-relaxed"
                  id="markdown-input"
                  placeholder="Start typing your markdown here..."
                ></textarea>
              </div>
              <div
                class="md-preview p-4 overflow-y-auto prose prose-invert prose-sm max-w-none prose-headings:font-semibold prose-h1:text-2xl prose-h2:text-xl prose-h3:text-lg prose-pre:bg-[#1e293b] prose-pre:p-3 prose-pre:rounded-md prose-code:geist-mono prose-code:text-sm"
                id="md-preview-pane"
              >
                <h1>Sample Header 1</h1>
                <p>
                  This is a paragraph with <strong>bold text</strong> and
                  <em>italic text</em>.
                </p>
                <h2>Sample Header 2</h2>
                <ul>
                  <li>Unordered list item 1</li>
                  <li>Unordered list item 2</li>
                </ul>
                <h3>Sample Header 3</h3>
                <ol>
                  <li>Ordered list item 1</li>
                  <li>Ordered list item 2</li>
                </ol>
                <p>
                  <a class="text-[#1383eb] hover:underline" href="#"
                    >This is a link</a
                  >
                </p>
                <pre
                  class="code-block geist-mono"
                ><code class="language-javascript"><span class="token comment">// This is a JavaScript code block</span>
<span class="token keyword">function</span> <span class="token function">greet</span><span class="token punctuation">(</span><span class="token parameter">name</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
  <span class="token keyword">return</span> <span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">Hello, </span><span class="token interpolation"><span class="token interpolation-punctuation punctuation">${</span>name<span class="token interpolation-punctuation punctuation">}</span></span><span class="token string">!</span><span class="token template-punctuation string">`</span></span><span class="token punctuation">;</span>
<span class="token punctuation">}</span></code></pre>
              </div>
            </div>
          </div>
          <aside
            class="w-full md:w-80 lg:w-96 bg-[#1c2127] rounded-lg shadow-xl flex flex-col"
          >
            <div class="p-4 border-b border-[#283139]">
              <h2 class="text-lg font-semibold text-white">Document Sync</h2>
              <p class="text-xs text-slate-400">
                Notes tied to document sections
              </p>
            </div>
            <div class="flex-1 overflow-y-auto p-2 space-y-1">
              <a
                class="flex items-center gap-3 p-3 rounded-md hover:bg-[#283139] transition-colors group"
                href="#"
              >
                <div
                  class="flex items-center justify-center shrink-0 size-10 rounded-lg bg-[#283139] group-hover:bg-[#1383eb] text-slate-300 group-hover:text-white transition-colors"
                >
                  <span class="material-icons-outlined text-xl"
                    >description</span
                  >
                </div>
                <div class="flex-1 overflow-hidden">
                  <p class="text-sm font-medium text-slate-100 truncate">
                    Introduction Notes
                  </p>
                  <p class="text-xs text-slate-400 truncate">
                    Linked to: Section 1 - Overview
                  </p>
                </div>
              </a>
              <a
                class="flex items-center gap-3 p-3 rounded-md hover:bg-[#283139] transition-colors group bg-[#232b33] border-l-2 border-[#1383eb]"
                href="#"
              >
                <div
                  class="flex items-center justify-center shrink-0 size-10 rounded-lg bg-[#1383eb] text-white transition-colors"
                >
                  <span class="material-icons-outlined text-xl"
                    >description</span
                  >
                </div>
                <div class="flex-1 overflow-hidden">
                  <p class="text-sm font-medium text-white truncate">
                    Key Concepts Analysis
                  </p>
                  <p class="text-xs text-slate-300 truncate">
                    Linked to: Section 2.3 - Core Ideas
                  </p>
                </div>
              </a>
              <a
                class="flex items-center gap-3 p-3 rounded-md hover:bg-[#283139] transition-colors group"
                href="#"
              >
                <div
                  class="flex items-center justify-center shrink-0 size-10 rounded-lg bg-[#283139] group-hover:bg-[#1383eb] text-slate-300 group-hover:text-white transition-colors"
                >
                  <span class="material-icons-outlined text-xl"
                    >description</span
                  >
                </div>
                <div class="flex-1 overflow-hidden">
                  <p class="text-sm font-medium text-slate-100 truncate">
                    Conclusion Summary
                  </p>
                  <p class="text-xs text-slate-400 truncate">
                    Linked to: Final Chapter
                  </p>
                </div>
              </a>
              <a
                class="flex items-center gap-3 p-3 rounded-md hover:bg-[#283139] transition-colors group"
                href="#"
              >
                <div
                  class="flex items-center justify-center shrink-0 size-10 rounded-lg bg-[#283139] group-hover:bg-[#1383eb] text-slate-300 group-hover:text-white transition-colors"
                >
                  <span class="material-icons-outlined text-xl"
                    >add_circle_outline</span
                  >
                </div>
                <div class="flex-1 overflow-hidden">
                  <p class="text-sm font-medium text-[#1383eb] truncate">
                    Create New Note
                  </p>
                </div>
              </a>
            </div>
          </aside>
        </main>
      </div>
    </div>
    <script>
      // Basic toggle for Edit/Preview on smaller screens.
      // A full Markdown library (like Marked.js or Showdown.js) would be needed for actual rendering.
      const editButton = document.getElementById("view-toggle-edit");
      const previewButton = document.getElementById("view-toggle-preview");
      const editorPane = document.getElementById("md-editor-pane");
      const previewPane = document.getElementById("md-preview-pane");
      function showEditor() {
        editorPane.classList.add("active");
        editorPane.classList.remove("md:hidden"); // ensure it's not hidden by responsive styles
        previewPane.classList.remove("active");
        if (window.innerWidth <= 768) {
          // md breakpoint
          previewPane.classList.add("hidden");
          editorPane.classList.remove("hidden");
        } else {
          // On larger screens, always show both
          previewPane.classList.remove("hidden");
          editorPane.classList.remove("hidden");
        }
        editButton.classList.add("bg-[#1383eb]", "text-white");
        editButton.classList.remove("hover:bg-[#283139]", "text-slate-300");
        previewButton.classList.remove("bg-[#1383eb]", "text-white");
        previewButton.classList.add("hover:bg-[#283139]", "text-slate-300");
      }
      function showPreview() {
        previewPane.classList.add("active");
        previewPane.classList.remove("md:hidden"); // ensure it's not hidden by responsive styles
        editorPane.classList.remove("active");
        if (window.innerWidth <= 768) {
          // md breakpoint
          editorPane.classList.add("hidden");
          previewPane.classList.remove("hidden");
        } else {
          // On larger screens, always show both
          editorPane.classList.remove("hidden");
          previewPane.classList.remove("hidden");
        }
        previewButton.classList.add("bg-[#1383eb]", "text-white");
        previewButton.classList.remove("hover:bg-[#283139]", "text-slate-300");
        editButton.classList.remove("bg-[#1383eb]", "text-white");
        editButton.classList.add("hover:bg-[#283139]", "text-slate-300");
        // Simulate markdown rendering (replace with actual library)
        // const markdownInput = document.getElementById('markdown-input').value;
        // For now, we'll just keep the static preview content.
        // previewPane.innerHTML = markdownInput; // Very basic, no actual MD parsing
      }
      editButton.addEventListener("click", showEditor);
      previewButton.addEventListener("click", showPreview);
      // Initial setup based on screen size
      function handleResize() {
        if (window.innerWidth > 768) {
          // md breakpoint
          editorPane.classList.remove("hidden");
          previewPane.classList.remove("hidden");
          editorPane.classList.add("active"); // Default to editor active on large screens
          previewPane.classList.add("active"); // Also show preview
        } else {
          // On small screens, check current active tab or default to editor
          if (previewPane.classList.contains("active")) {
            showPreview();
          } else {
            showEditor();
          }
        }
      }
      window.addEventListener("resize", handleResize);
      document.addEventListener("DOMContentLoaded", () => {
        // Set initial state for split view based on screen size
        if (window.innerWidth <= 768) {
          // If small screen, default to editor view only
          showEditor();
        } else {
          // If large screen, show both
          editorPane.classList.add("active");
          previewPane.classList.add("active"); // Show preview
          editorPane.classList.remove("hidden");
          previewPane.classList.remove("hidden");
          editButton.classList.add("bg-[#1383eb]", "text-white"); // Keep edit as "selected"
          previewButton.classList.add("hover:bg-[#283139]", "text-slate-300");
        }
      });
    </script>
  </body>
</html>
