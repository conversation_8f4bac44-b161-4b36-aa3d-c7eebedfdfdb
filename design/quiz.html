<html>
  <head>
    <meta charset="utf-8" />
    <link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect" />
    <link
      as="style"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Manrope%3Awght%40400%3B500%3B700%3B800&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900"
      onload="this.rel='stylesheet'"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined"
      rel="stylesheet"
    />
    <title>StudyAI - Quiz</title>
    <link href="data:image/x-icon;base64," rel="icon" type="image/x-icon" />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <style>
      .material-icons-outlined {
        font-weight: normal;
        font-style: normal;
        font-size: 24px;
        display: inline-block;
        line-height: 1;
        text-transform: none;
        letter-spacing: normal;
        word-wrap: normal;
        white-space: nowrap;
        direction: ltr;
        -webkit-font-smoothing: antialiased;
        text-rendering: optimizeLegibility;
        -moz-osx-font-smoothing: grayscale;
        font-feature-settings: "liga";
      }
      .correct-answer {
        border: 2px solid #22c55e;
      }
      .incorrect-answer {
        border: 2px solid #ef4444;
      }
      .selected-answer {
        border: 2px solid #f59e0b;
      }
    </style>
  </head>
  <body
    class="bg-slate-50"
    style="font-family: Manrope, 'Noto Sans', sans-serif"
  >
    <div
      class="relative flex size-full min-h-screen flex-col group/design-root overflow-x-hidden"
    >
      <div class="flex flex-col h-full grow">
        <header
          class="flex items-center justify-between whitespace-nowrap border-b border-solid border-slate-200 px-6 py-3 bg-white sticky top-0 z-50"
        >
          <div class="flex items-center gap-3 text-slate-900">
            <div class="size-7 text-blue-600">
              <svg
                fill="none"
                viewBox="0 0 48 48"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g clip-path="url(#clip0_6_543)">
                  <path
                    d="M42.1739 20.1739L27.8261 5.82609C29.1366 7.13663 28.3989 10.1876 26.2002 13.7654C24.8538 15.9564 22.9595 18.3449 20.6522 20.6522C18.3449 22.9595 15.9564 24.8538 13.7654 26.2002C10.1876 28.3989 7.13663 29.1366 5.82609 27.8261L20.1739 42.1739C21.4845 43.4845 24.5355 42.7467 28.1133 40.548C30.3042 39.2016 32.6927 37.3073 35 35C37.3073 32.6927 39.2016 30.3042 40.548 28.1133C42.7467 24.5355 43.4845 21.4845 42.1739 20.1739Z"
                    fill="currentColor"
                  ></path>
                  <path
                    clip-rule="evenodd"
                    d="M7.24189 26.4066C7.31369 26.4411 7.64204 26.5637 8.52504 26.3738C9.59462 26.1438 11.0343 25.5311 12.7183 24.4963C14.7583 23.2426 17.0256 21.4503 19.238 19.238C21.4503 17.0256 23.2426 14.7583 24.4963 12.7183C25.5311 11.0343 26.1438 9.59463 26.3738 8.52504C26.5637 7.64204 26.4411 7.31369 26.4066 7.24189C26.345 7.21246 26.143 7.14535 25.6664 7.1918C24.9745 7.25925 23.9954 7.5498 22.7699 8.14278C20.3369 9.32007 17.3369 11.4915 14.4142 14.4142C11.4915 17.3369 9.32007 20.3369 8.14278 22.7699C7.5498 23.9954 7.25925 24.9745 7.1918 25.6664C7.14534 26.143 7.21246 26.345 7.24189 26.4066ZM29.9001 10.7285C29.4519 12.0322 28.7617 13.4172 27.9042 14.8126C26.465 17.1544 24.4686 19.6641 22.0664 22.0664C19.6641 24.4686 17.1544 26.465 14.8126 27.9042C13.4172 28.7617 12.0322 29.4519 10.7285 29.9001L21.5754 40.747C21.6001 40.7606 21.8995 40.931 22.8729 40.7217C23.9424 40.4916 25.3821 39.879 27.0661 38.8441C29.1062 37.5904 31.3734 35.7982 33.5858 33.5858C35.7982 31.3734 37.5904 29.1062 38.8441 27.0661C39.879 25.3821 40.4916 23.9425 40.7216 22.8729C40.931 21.8995 40.7606 21.6001 40.747 21.5754L29.9001 10.7285ZM29.2403 4.41187L43.5881 18.7597C44.9757 20.1473 44.9743 22.1235 44.6322 23.7139C44.2714 25.3919 43.4158 27.2666 42.252 29.1604C40.8128 31.5022 38.8165 34.012 36.4142 36.4142C34.012 38.8165 31.5022 40.8128 29.1604 42.252C27.2666 43.4158 25.3919 44.2714 23.7139 44.6322C22.1235 44.9743 20.1473 44.9757 18.7597 43.5881L4.41187 29.2403C3.29027 28.1187 3.08209 26.5973 3.21067 25.2783C3.34099 23.9415 3.8369 22.4852 4.54214 21.0277C5.96129 18.0948 8.43335 14.7382 11.5858 11.5858C14.7382 8.43335 18.0948 5.9613 21.0277 4.54214C22.4852 3.8369 23.9415 3.34099 25.2783 3.21067C26.5973 3.08209 28.1187 3.29028 29.2403 4.41187Z"
                    fill="currentColor"
                    fill-rule="evenodd"
                  ></path>
                </g>
                <defs>
                  <clipPath id="clip0_6_543">
                    <rect fill="white" height="48" width="48"></rect>
                  </clipPath>
                </defs>
              </svg>
            </div>
            <h1
              class="text-slate-900 text-xl font-bold leading-tight tracking-tight"
            >
              StudyAI
            </h1>
          </div>
          <div class="flex items-center gap-6">
            <nav class="flex items-center gap-6">
              <a
                class="text-slate-700 text-sm font-medium leading-normal hover:text-blue-600 transition-colors"
                href="#"
                >My Library</a
              >
              <a
                class="text-slate-700 text-sm font-medium leading-normal hover:text-blue-600 transition-colors"
                href="#"
                >Explore</a
              >
            </nav>
            <button
              class="flex items-center justify-center rounded-full h-10 w-10 bg-slate-100 text-slate-700 hover:bg-slate-200 transition-colors"
            >
              <span class="material-icons-outlined text-xl">notifications</span>
            </button>
            <div
              class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10 border-2 border-slate-200"
              style="
                background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuD1jvprCNcKridpzgddaU571PrSZ8ydHA4ribSne3CCh4dDqiGA6A3jsUKTajl7SlHcsyBb404nAmn-QkLVK58oUpsJ1aJopiGDh8yyMz2gxj69xu0FzDInh2VunP45UEbqvWBn5-NofzoTGFvQQW2NA699WDy90gjzKhk52P0s03dP7YvzaPy6imuTLaIfrHRBCfJi2TL0wkFH6M3byxllJRLs9U-qAR-wgOp3f-RNQxYDxH-nobpS-_C2b03KXiaLZHSKvNUmZQK6');
              "
            ></div>
          </div>
        </header>
        <main class="flex flex-1 overflow-hidden">
          <div class="flex flex-col flex-1 p-6 overflow-y-auto">
            <div class="mb-6">
              <div class="flex items-center gap-2 text-sm text-slate-500 mb-2">
                <a class="hover:text-blue-600" href="#">My Library</a>
                <span>/</span>
                <a class="hover:text-blue-600" href="#">Quizzes</a>
                <span>/</span>
                <span class="font-medium text-slate-700">Quiz 1</span>
              </div>
              <h2 class="text-slate-900 text-3xl font-bold leading-tight">
                Quiz 1
              </h2>
              <p class="text-slate-500 text-sm">Question 3 of 10</p>
            </div>
            <div
              class="flex-1 bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden p-8"
            >
              <div class="mb-6">
                <div class="flex justify-between items-center mb-1">
                  <p class="text-slate-700 text-sm font-medium">Progress</p>
                  <p class="text-slate-700 text-sm font-semibold">30%</p>
                </div>
                <div class="w-full bg-slate-200 rounded-full h-2.5">
                  <div
                    class="bg-blue-600 h-2.5 rounded-full"
                    style="width: 30%"
                  ></div>
                </div>
              </div>
              <h1
                class="text-slate-900 text-2xl font-bold leading-tight tracking-tight mb-8"
              >
                What is the main purpose of the circulatory system?
              </h1>
              <div class="space-y-4 mb-10">
                <button
                  class="w-full text-left py-3 px-5 rounded-lg bg-slate-100 hover:bg-slate-200 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 text-slate-800 selected-answer"
                >
                  To transport oxygen and nutrients throughout the body
                </button>
                <button
                  class="w-full text-left py-3 px-5 rounded-lg bg-slate-100 hover:bg-slate-200 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 text-slate-800"
                >
                  To filter waste products from the blood
                </button>
                <button
                  class="w-full text-left py-3 px-5 rounded-lg bg-slate-100 hover:bg-slate-200 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 text-slate-800 correct-answer"
                >
                  To regulate body temperature
                </button>
                <button
                  class="w-full text-left py-3 px-5 rounded-lg bg-slate-100 hover:bg-slate-200 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 text-slate-800 incorrect-answer"
                >
                  To produce hormones
                </button>
              </div>
              <div
                class="flex flex-col sm:flex-row justify-between items-center gap-4"
              >
                <button
                  class="w-full sm:w-auto py-3 px-6 rounded-lg bg-slate-100 hover:bg-slate-200 transition-colors text-sm font-bold text-slate-700"
                >
                  Previous
                </button>
                <button
                  class="w-full sm:w-auto py-3 px-6 rounded-lg bg-blue-600 hover:bg-blue-700 transition-colors text-sm font-bold text-white"
                >
                  Next
                </button>
              </div>
              <div class="mt-12 text-center border-t border-slate-200 pt-8">
                <p class="text-3xl font-bold mb-2 text-slate-900">
                  Score: <span class="text-blue-600">85%</span>
                </p>
                <button
                  class="py-3 px-6 rounded-lg bg-blue-600 hover:bg-blue-700 transition-colors text-sm font-bold text-white"
                >
                  Review Answers
                </button>
                <button
                  class="mt-4 w-full sm:w-auto py-3 px-6 rounded-lg bg-green-500 hover:bg-green-600 transition-colors text-sm font-bold text-white"
                >
                  Finish Quiz
                </button>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  </body>
</html>
