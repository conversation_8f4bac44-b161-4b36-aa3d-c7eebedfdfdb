<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect" />
    <link
      as="style"
      href="https://fonts.googleapis.com/css2?display=swap&amp;family=Lexend%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900"
      onload="this.rel='stylesheet'"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/icon?family=Material+Icons"
      rel="stylesheet"
    />
    <title>StudySmart Flashcards</title>
    <link href="data:image/x-icon;base64," rel="icon" type="image/x-icon" />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <style>
      .flip-card {
        perspective: 1000px;
      }
      .flip-card-inner {
        position: relative;
        width: 100%;
        height: 100%;
        text-align: center;
        transition: transform 0.6s;
        transform-style: preserve-3d;
      }
      .flip-card:hover .flip-card-inner,
      .flip-card.flipped .flip-card-inner {
        transform: rotateY(180deg);
      }
      .flip-card-front,
      .flip-card-back {
        position: absolute;
        width: 100%;
        height: 100%;
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border-radius: 0.5rem;
      }
      .flip-card-front {
        background-color: white;
        color: #0f172a;
        border: 1px solid #e2e8f0;
      }
      .flip-card-back {
        background-color: white;
        color: #0f172a;
        border: 1px solid #e2e8f0;
        transform: rotateY(180deg);
      }
    </style>
  </head>
  <body class="antialiased">
    <div
      class="relative flex size-full min-h-screen flex-col bg-slate-50 group/design-root overflow-x-hidden"
      style="font-family: Lexend, 'Noto Sans', sans-serif"
    >
      <div class="layout-container flex h-full grow flex-col">
        <header
          class="flex items-center justify-between whitespace-nowrap border-b border-solid border-slate-200 px-6 sm:px-10 py-4 bg-white"
        >
          <div class="flex items-center gap-3 sm:gap-4 text-slate-900">
            <div class="size-5 sm:size-6 text-blue-600">
              <svg
                fill="none"
                viewBox="0 0 48 48"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M24 45.8096C19.6865 45.8096 15.4698 44.5305 11.8832 42.134C8.29667 39.7376 5.50128 36.3314 3.85056 32.3462C2.19985 28.361 1.76794 23.9758 2.60947 19.7452C3.451 15.5145 5.52816 11.6284 8.57829 8.5783C11.6284 5.52817 15.5145 3.45101 19.7452 2.60948C23.9758 1.76795 28.361 2.19986 32.3462 3.85057C36.3314 5.50129 39.7376 8.29668 42.134 11.8833C44.5305 15.4698 45.8096 19.6865 45.8096 24L24 24L24 45.8096Z"
                  fill="currentColor"
                ></path>
              </svg>
            </div>
            <h2
              class="text-slate-900 text-xl sm:text-2xl font-bold leading-tight tracking-[-0.015em]"
            >
              StudySmart
            </h2>
          </div>
          <div class="flex flex-1 justify-end items-center gap-4 sm:gap-6">
            <nav class="hidden sm:flex items-center gap-6">
              <a
                class="text-slate-700 hover:text-blue-600 text-sm font-medium leading-normal transition-colors"
                href="#"
                >Home</a
              >
              <a
                class="text-slate-700 hover:text-blue-600 text-sm font-medium leading-normal transition-colors"
                href="#"
                >Decks</a
              >
              <a
                class="text-slate-700 hover:text-blue-600 text-sm font-medium leading-normal transition-colors"
                href="#"
                >Library</a
              >
              <a
                class="text-slate-700 hover:text-blue-600 text-sm font-medium leading-normal transition-colors"
                href="#"
                >Community</a
              >
            </nav>
            <button
              aria-label="Notifications"
              class="flex items-center justify-center rounded-full h-10 w-10 bg-slate-100 text-slate-700 hover:bg-slate-200 transition-colors"
            >
              <span class="material-icons text-xl">notifications</span>
            </button>
            <div
              class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10 border-2 border-slate-200"
              style="
                background-image: url('https://lh3.googleusercontent.com/aida-public/AB6AXuCaJ7eMrDMXrT6WQcBD4Nm1jEqrDdmdK7mhUi1YJ38Lww7mbUs74FE60lPBXTZFvvv6rei5SZHTByF5bNRhHRB5a5bVoW8yIj2Lc9TBHGq_oC2vmIZUobCG1N_OwUhTm77K_IftrrvFlAPxknPtAFxF1QO032xtdP2eXwo8JyC-X8QLsiZBSgiern8L5qvUMovXGlC8Ieu1a7Pk1KILcLfmGBrJVbbuZ9uV0ei5OU-2rORzmqD3N2gworik5USvVxVMUMl4XdFpkcN2');
              "
            ></div>
          </div>
        </header>
        <main
          class="px-4 sm:px-10 md:px-20 lg:px-40 flex flex-1 flex-col items-center py-8 sm:py-12"
        >
          <div
            class="layout-content-container flex flex-col max-w-[768px] w-full flex-1"
          >
            <div class="mb-6 sm:mb-8 text-center">
              <h1
                class="text-slate-900 tracking-tight text-3xl sm:text-4xl font-bold leading-tight"
              >
                Introduction to Psychology
              </h1>
            </div>
            <div class="mb-6">
              <div class="flex justify-between items-center mb-2">
                <p class="text-slate-700 text-sm font-medium">
                  Card <span id="card-counter">1 of 15</span>
                </p>
                <p class="text-slate-700 text-sm font-semibold">
                  <span id="progress-percentage">6%</span> Complete
                </p>
              </div>
              <div class="h-2.5 rounded-full bg-slate-200 w-full">
                <div
                  class="h-2.5 rounded-full bg-blue-600"
                  id="progress-bar"
                  style="width: 6%"
                ></div>
              </div>
            </div>
            <div
              class="flip-card h-80 sm:h-96 w-full cursor-pointer mb-6"
              id="flashcard"
              onclick="this.classList.toggle('flipped')"
            >
              <div class="flip-card-inner shadow-lg">
                <div
                  class="flip-card-front p-6 sm:p-8 flex flex-col justify-center items-center text-center"
                >
                  <p
                    class="text-slate-900 text-xl sm:text-2xl font-semibold mb-4"
                  >
                    What is the definition of cognitive dissonance?
                  </p>
                  <p class="text-slate-500 text-sm">Click to reveal</p>
                </div>
                <div
                  class="flip-card-back p-6 sm:p-8 flex flex-col justify-center items-center text-center"
                >
                  <p class="text-slate-900 text-lg sm:text-xl font-medium mb-6">
                    Cognitive dissonance is the mental discomfort experienced by
                    a person who holds two or more contradictory beliefs, ideas,
                    or values.
                  </p>
                  <div class="flex gap-4">
                    <button
                      class="flex items-center justify-center rounded-lg h-10 px-4 bg-green-600 hover:bg-green-700 text-white text-sm font-semibold leading-normal transition-colors"
                    >
                      <span class="material-icons mr-2 text-lg"
                        >check_circle</span
                      >
                      Got it
                    </button>
                    <button
                      class="flex items-center justify-center rounded-lg h-10 px-4 bg-orange-500 hover:bg-orange-600 text-white text-sm font-semibold leading-normal transition-colors"
                    >
                      <span class="material-icons mr-2 text-lg">replay</span>
                      Review again
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div class="flex justify-between items-center mb-6">
              <button
                class="flex items-center justify-center rounded-lg h-11 px-5 bg-white border border-slate-300 hover:bg-slate-50 text-slate-700 text-sm font-bold leading-normal tracking-[0.015em] transition-colors"
              >
                <span class="material-icons text-xl">arrow_back</span>
                <span class="ml-2 hidden sm:inline">Previous</span>
              </button>
              <button
                class="flex items-center justify-center rounded-lg h-11 px-5 bg-white border border-slate-300 hover:bg-slate-50 text-slate-700 text-sm font-bold leading-normal tracking-[0.015em] transition-colors"
              >
                <span class="mr-2 hidden sm:inline">Next</span>
                <span class="material-icons text-xl">arrow_forward</span>
              </button>
            </div>
            <div class="flex flex-col sm:flex-row justify-center gap-4">
              <button
                class="flex flex-1 sm:flex-initial items-center justify-center rounded-lg h-11 px-5 bg-blue-600 hover:bg-blue-700 text-white text-sm font-bold leading-normal tracking-[0.015em] transition-colors"
              >
                <span class="material-icons mr-2 text-xl">shuffle</span>
                Shuffle Deck
              </button>
              <button
                class="flex flex-1 sm:flex-initial items-center justify-center rounded-lg h-11 px-5 bg-red-600 hover:bg-red-700 text-white text-sm font-bold leading-normal tracking-[0.015em] transition-colors"
              >
                <span class="material-icons mr-2 text-xl">restart_alt</span>
                Reset Deck
              </button>
            </div>
          </div>
        </main>
      </div>
    </div>
    <script>
      // Example: How to update card counter (this would be dynamic)
      // const cardCounter = document.getElementById('card-counter');
      // cardCounter.textContent = '2 of 15';
      // Example: How to update progress bar (this would be dynamic)
      // const progressBar = document.getElementById('progress-bar');
      // const progressPercentage = document.getElementById('progress-percentage');
      // const newProgress = 20; // percentage
      // progressBar.style.width = newProgress + '%';
      // progressPercentage.textContent = newProgress + '% Complete';
      // Add event listener to flip card on click (already handled by inline onclick for simplicity here)
      // const flashcard = document.getElementById('flashcard');
      // flashcard.addEventListener('click', () => {
      //   flashcard.classList.toggle('flipped');
      // });
    </script>
  </body>
</html>
